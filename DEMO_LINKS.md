# 🌐 PrintMind 在线演示

## 🚀 一键体验

### 方式1：直接访问在线演示
```
🔗 演示地址: [即将上线]
📱 移动端: [即将上线]
```

### 方式2：一键部署到你的云端
点击下面的按钮，3分钟内拥有你自己的PrintMind：

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/H2Cat96/PrintMind&project-name=printmind&repository-name=printmind)

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/new/template?template=https://github.com/H2Cat96/PrintMind&envs=DEEPSEEK_API_KEY)

[![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy?repo=https://github.com/H2Cat96/PrintMind)

## 📋 快速部署步骤

### Vercel 前端部署（3分钟）
1. 点击上方 Vercel 按钮
2. 使用 GitHub 登录
3. 确认仓库导入
4. 等待自动部署完成
5. 获得访问链接

### Railway 后端部署（5分钟）
1. 点击上方 Railway 按钮
2. 使用 GitHub 登录
3. 选择 PrintMind 仓库
4. 添加环境变量：`DEEPSEEK_API_KEY`
5. 等待部署完成

### 完整配置（可选）
如果你想启用 AI 功能：
1. 注册 [DeepSeek](https://platform.deepseek.com/) 账号
2. 获取 API Key
3. 在部署平台添加环境变量

## 🎯 分享你的部署

部署完成后，你可以这样分享：

### 分享模板
```
🎨 我部署了一个智能排版工具！

✨ 功能：
• 📄 Markdown/Word 转 PDF
• 🤖 AI 智能优化排版
• 🎨 可视化配置界面
• 📱 支持移动端

🔗 立即体验：[你的部署链接]
📚 项目源码：https://github.com/H2Cat96/PrintMind

完全免费，打开即用！
```

## 🔧 自定义配置

### 环境变量说明
```bash
# 必需（启用AI功能）
DEEPSEEK_API_KEY=your_api_key

# 可选
DEBUG=false
MAX_FILE_SIZE=52428800
PDF_DPI=300
```

### 自定义域名
- Vercel: 项目设置 → Domains
- Railway: 项目设置 → Custom Domain
- Render: 项目设置 → Custom Domains

## 📊 使用统计

部署后可以查看：
- 访问量统计
- 用户使用情况
- 错误日志
- 性能监控

## 🆘 部署问题

### 常见问题
1. **构建失败**: 检查 Node.js 版本
2. **API 连接失败**: 确认后端 URL 配置
3. **字体加载问题**: 检查静态资源路径

### 获取帮助
- 📖 查看 [ONLINE_DEPLOYMENT.md](ONLINE_DEPLOYMENT.md)
- 🐛 提交 [GitHub Issue](https://github.com/H2Cat96/PrintMind/issues)
- 💬 参与 [讨论](https://github.com/H2Cat96/PrintMind/discussions)

## 🌟 成功案例

### 用户反馈
> "3分钟就部署好了，太方便了！" - 用户A

> "界面很漂亮，功能很实用" - 用户B

> "AI优化功能很棒，排版效果专业" - 用户C

### 使用场景
- 📚 学术论文排版
- 📊 商业报告制作
- 📝 技术文档编写
- 🎓 教学材料准备

---

**立即部署，开始你的智能排版之旅！** 🚀
