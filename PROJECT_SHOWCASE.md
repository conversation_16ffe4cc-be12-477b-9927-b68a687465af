# 🎨 PrintMind 项目展示

## 📱 项目概览

PrintMind 是一个现代化的智能排版工具，让文档排版变得简单而专业。

### 🎯 核心价值
- **专业性**: 支持印刷级 PDF 输出
- **易用性**: 直观的可视化配置界面
- **高效性**: 一键部署，快速上手
- **灵活性**: 多格式支持，自定义配置

## 🌟 主要特性

### 📄 多格式支持
- ✅ Markdown 文档
- ✅ Word 文档 (.docx)
- ✅ 纯文本文件
- ✅ 拖拽上传

### 🎨 专业排版
- ✅ 可视化页面配置
- ✅ 字体、边距、行距调整
- ✅ 多种页面格式 (A4, A3, Letter, Legal)
- ✅ CMYK 印刷色彩模式



### 📊 实时预览
- ✅ HTML 预览模式
- ✅ PDF 预览模式
- ✅ 缩放和导航
- ✅ 即时更新

## 🛠️ 技术亮点

### 前端技术栈
```
Vue 3 + TypeScript + Tailwind CSS
- 组合式 API
- 响应式设计
- 现代化 UI 组件
- 类型安全
```

### 后端技术栈
```
FastAPI + WeasyPrint + Python
- 高性能异步 API
- 专业 PDF 生成
- 自动 API 文档
- 类型验证
```

### 部署方案
```
Docker + Docker Compose
- 容器化部署
- 一键启动
- 环境隔离
- 易于扩展
```

## 🚀 快速体验

### 一行命令启动
```bash
git clone https://github.com/H2Cat96/PrintMind.git && cd PrintMind && docker-compose up -d
```

### 访问地址
- 🌐 前端界面: http://localhost
- 📡 API 文档: http://localhost:8000/docs
- 🔧 后端服务: http://localhost:8000

## 📈 使用场景

### 👨‍💼 商务文档
- 商业计划书
- 项目报告
- 合同文档
- 宣传材料

### 📚 教育领域
- 教学课件
- 学术论文
- 考试试卷
- 学习资料

### 📝 内容创作
- 技术博客
- 用户手册
- 产品说明
- 新闻稿件

### 🏢 企业应用
- 内部文档
- 培训材料
- 流程规范
- 年度报告

## 🎁 开源优势

### 🔓 完全开源
- MIT 许可证
- 源码透明
- 自由使用
- 商业友好

### 🤝 社区驱动
- 欢迎贡献
- 问题反馈
- 功能建议
- 技术交流

### 📚 学习价值
- 现代 Web 开发
- 微服务架构
- AI 集成实践
- 容器化部署

## 🏆 项目优势

### vs 传统排版工具
| 特性 | PrintMind | 传统工具 |
|------|-----------|----------|
| 🌐 Web 访问 | ✅ | ❌ |
| 🐳 容器部署 | ✅ | ❌ |
| 📱 响应式设计 | ✅ | ❌ |
| 🔓 开源免费 | ✅ | ❌ |
| 🎨 专业排版 | ✅ | ❌ |

### vs 在线服务
| 特性 | PrintMind | 在线服务 |
|------|-----------|----------|
| 🔒 数据隐私 | ✅ | ❌ |
| 🎨 自定义扩展 | ✅ | ❌ |
| 💰 使用成本 | 免费 | 付费 |
| 🌐 离线使用 | ✅ | ❌ |
| 🔧 功能定制 | ✅ | ❌ |

## 📞 获取支持

### 🐛 问题反馈
- GitHub Issues: [提交问题](https://github.com/H2Cat96/PrintMind/issues)
- 功能请求: [提交建议](https://github.com/H2Cat96/PrintMind/issues/new)

### 💬 技术交流
- 讨论区: [参与讨论](https://github.com/H2Cat96/PrintMind/discussions)
- 代码贡献: [提交 PR](https://github.com/H2Cat96/PrintMind/pulls)

### 📖 文档资源
- 📋 使用指南: [README.md](README.md)
- 🏗️ 架构说明: [ARCHITECTURE.md](ARCHITECTURE.md)
- 🚀 部署指南: [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)

---

## 🌟 立即开始

```bash
# 克隆项目
git clone https://github.com/H2Cat96/PrintMind.git

# 进入目录
cd PrintMind

# 一键启动
docker-compose up -d

# 打开浏览器访问
open http://localhost
```

**让智能排版改变你的文档创作体验！** ✨
